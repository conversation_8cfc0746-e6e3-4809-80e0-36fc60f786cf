from typing import Dict, List, Tuple, Optional

# Predefined maritime routes
PREDEFINED_ROUTES = {
    "Gopalpur_to_New_Harbour": {
        "name": "Gopalpur to New Harbour",
        "waypoints": [
            [30.213982, 32.557983],
            [30.318359, 32.382202],
            [30.945814, 32.306671],
            [31.298117, 32.387159],
            [31.7, 32.1],
            [32.316071, 30.408377],
            [32.863395, 28.905525],
            [33.115811, 28.212434],
            [33.219565, 27.927542],
            [33.328, 27.6298],
            [33.748752, 26.306431],
            [34.011915, 25.478721],
            [34.187436, 24.926664],
            [34.8, 23.0],
            [35.126694, 21.407365],
            [35.845726, 17.902084],
            [36.086854, 16.726588],
            [36.4, 15.2],
            [36.907095, 13.263819],
            [37.209117, 12.110644],
            [37.212689, 12.097004],
            [37.215493, 12.086301],
            [37.283186, 11.827836],
            [37.454891, 11.172235],
            [37.5, 11.0],
            [37.489085, 10.372293],
            [37.4851, 10.1431],
            [37.4, 7.5],
            [37.2, 3.1],
            [36.666667, -0.366667],
            [36.473171, -1.62439],
            [36.377724, -2.244793],
            [36.324512, -2.590675],
            [36.220888, -3.264225],
            [36.158352, -3.670714],
            [36.156455, -3.683043],
            [36.0, -4.7],
            [35.97289, -5.269383],
            [35.968819, -5.354867],
            [35.95, -5.75],
            [36.31906, -7.26966],
            [36.549727, -8.219465],
            [36.8, -9.25],
            [36.83741, -9.36445],
            [37.324914, -10.855872],
            [37.417342, -11.138637],
            [37.717697, -12.057515],
            [38.272734, -13.755544],
            [38.5182, -14.5065],
            [40.0, -20.0],
            [41.125083, -25.565029],
            [41.1999, -25.9351],
            [41.584862, -28.584901],
            [41.790603, -30.001074],
            [42.072366, -31.940532],
            [42.0901, -32.0626],
            [42.340269, -34.863425],
            [42.592324, -37.685353],
            [42.6501, -38.3322],
            [42.706999, -40.001623],
            [42.796183, -42.618304],
            [42.8665, -44.6814],
            [42.807466, -47.493255],
            [42.754804, -50.001652],
            [42.733, -51.0402],
            [42.618612, -52.53975],
            [42.613022, -52.613022],
            [42.2526, -57.3379],
            [41.53046, -62.794754],
            [41.4359, -63.5093],
            [41.222837, -64.632986],
            [40.435954, -68.782982],
            [40.430133, -68.813685],
            [40.413733, -68.900173],
            [40.412386, -68.907278],
            [40.311722, -69.43818],
            [40.3, -69.5],
            [40.419295, -71.289425],
            [40.437172, -71.557579],
            [40.453237, -71.798554],
            [40.535177, -73.027658],
            [40.6, -74.0],
            [40.6061, -74.0456],
            [40.6285, -74.0561],
            [40.6676, -74.0488],
            [40.7081, -73.9779]
        ]
    },
    "Rotterdam_to_Singapore": {
        "name": "Rotterdam to Singapore",
        "waypoints": [
            [30.213982, 32.557983],
            [29.7, 32.6],
            [27.9, 33.75],
            [27.0, 34.5],
            [23.6, 37.0],
            [20.75, 38.9],
            [16.3, 41.2],
            [15.0, 42.0],
            [12.7, 43.3],
            [12.40439, 43.746586],
            [12.0, 45.0],
            [13.0, 51.0],
            [12.577758, 53.059021],
            [12.2395, 54.7085],
            [11.4317, 58.3951],
            [11.083455, 59.894005],
            [10.866984, 60.825733],
            [10.5802, 62.0601],
            [10.031585, 64.303249],
            [9.934828, 64.698862],
            [9.862937, 64.992809],
            [9.6889, 65.7044],
            [8.881605, 68.858995],
            [8.7613, 69.3291],
            [8.6701, 69.671733],
            [8.582747, 69.999915],
            [8.365148, 70.817426],
            [8.356493, 70.84994],
            [7.8014, 72.9354],
            [6.966807, 75.966807],
            [6.8131, 76.5251],
            [5.8, 80.1],
            [5.9, 81.9],
            [6.1983, 85.9479],
            [6.4664, 90.0],
            [6.7, 94.0],
            [7.0, 97.0],
            [3.2, 100.6],
            [2.0, 102.0],
            [1.1, 103.6]
        ]
    }
}

def get_predefined_route(route_id: str) -> Optional[Dict[str, any]]:
    """Get a predefined route by its ID."""
    return PREDEFINED_ROUTES.get(route_id)

def list_predefined_routes() -> Dict[str, str]:
    """Get a list of all predefined routes with their names."""
    return {route_id: route_data["name"] for route_id, route_data in PREDEFINED_ROUTES.items()}
