/* Leaflet container needs explicit dimensions */
.leaflet-container {
  width: 100% !important;
  height: 100% !important;
  min-height: 500px;
  position: absolute !important;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

/* Ensure the map container has a defined height */
.map-container {
  width: 100%;
  height: 100%;
  min-height: 500px;
  position: relative;
  overflow: hidden;
}

/* Fix for marker icons */
.leaflet-div-icon {
  background: transparent;
  border: none;
}

/* Fix for popup close button */
.leaflet-popup-close-button {
  font-size: 24px !important;
  padding: 8px !important;
}

/* Customize popup */
.leaflet-popup-content {
  margin: 10px 14px;
  line-height: 1.4;
}

.leaflet-popup-content-wrapper {
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

/* Customize the route line */
.leaflet-interactive {
  stroke-linecap: round;
  stroke-linejoin: round;
}

/* Fix for touch devices */
.leaflet-touch .leaflet-control-layers,
.leaflet-touch .leaflet-bar {
  border: 2px solid rgba(0, 0, 0, 0.2);
  background-clip: padding-box;
}
