import React from 'react';
import { Box } from '@mui/material';
import VoyageOptimizationDashboard from '../Voyage/VoyageOptimizationDashboard';

interface ShipDashboardProps {
  selectedRoute?: any;
  weatherLocations?: any[];
  onWindDataChange?: (windData: any[], showWindLayer: boolean) => void;
}

const ShipDashboard: React.FC<ShipDashboardProps> = ({ selectedRoute, weatherLocations, onWindDataChange }) => {
  return (
    <Box>
      <VoyageOptimizationDashboard
        selectedRoute={selectedRoute}
        weatherLocations={weatherLocations}
        onWindDataChange={onWindDataChange}
      />
    </Box>
  );
};

export default ShipDashboard;
