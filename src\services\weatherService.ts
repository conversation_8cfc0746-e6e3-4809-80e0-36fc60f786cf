export interface WeatherData {
  location: {
    name: string;
    lat: number;
    lng: number;
  };
  current: {
    temperature: number;
    humidity: number;
    windSpeed: number;
    windDirection: number;
    visibility: number;
    pressure: number;
    condition: string;
    icon: string;
  };
  marine: {
    waveHeight: number;
    swellHeight: number;
    swellDirection: number;
    seaTemperature: number;
    tideLevel: string;
  };
  forecast: Array<{
    date: string;
    temperature: { min: number; max: number };
    condition: string;
    windSpeed: number;
    waveHeight: number;
  }>;
  alerts: Array<{
    type: 'storm' | 'fog' | 'high_waves' | 'strong_wind';
    severity: 'low' | 'medium' | 'high';
    message: string;
    validUntil: string;
  }>;
}

// Mock weather data generator for demo purposes
// In production, this would call a real weather API like OpenWeatherMap
const generateMockWeatherData = (lat: number, lng: number, locationName: string): WeatherData => {
  // Generate realistic weather based on location
  const isTropical = lat >= -23.5 && lat <= 23.5;
  const isNorthern = lat > 0;
  
  // Base temperature on latitude and season
  const baseTemp = isTropical ? 28 : isNorthern ? 15 : 20;
  const tempVariation = Math.random() * 10 - 5;
  const temperature = Math.round(baseTemp + tempVariation);
  
  // Generate wind data
  const windSpeed = Math.round(5 + Math.random() * 25); // 5-30 knots
  const windDirection = Math.round(Math.random() * 360);
  
  // Generate wave data based on wind
  const waveHeight = Math.round((windSpeed / 10 + Math.random() * 2) * 10) / 10;
  const swellHeight = Math.round((waveHeight * 0.7 + Math.random() * 1) * 10) / 10;
  
  // Generate weather condition
  const conditions = ['Clear', 'Partly Cloudy', 'Cloudy', 'Light Rain', 'Moderate Rain', 'Thunderstorms'];
  const weights = [0.3, 0.25, 0.2, 0.15, 0.08, 0.02]; // Probability weights
  let condition = 'Clear';
  const rand = Math.random();
  let cumulative = 0;
  for (let i = 0; i < conditions.length; i++) {
    cumulative += weights[i];
    if (rand <= cumulative) {
      condition = conditions[i];
      break;
    }
  }
  
  // Generate alerts based on conditions
  const alerts: WeatherData['alerts'] = [];
  if (windSpeed > 25) {
    alerts.push({
      type: 'strong_wind',
      severity: windSpeed > 35 ? 'high' : 'medium',
      message: `Strong winds expected: ${windSpeed} knots`,
      validUntil: new Date(Date.now() + 6 * 60 * 60 * 1000).toISOString()
    });
  }
  if (waveHeight > 3) {
    alerts.push({
      type: 'high_waves',
      severity: waveHeight > 5 ? 'high' : 'medium',
      message: `High waves: ${waveHeight}m`,
      validUntil: new Date(Date.now() + 12 * 60 * 60 * 1000).toISOString()
    });
  }
  if (condition.includes('Thunderstorms')) {
    alerts.push({
      type: 'storm',
      severity: 'high',
      message: 'Thunderstorms in the area',
      validUntil: new Date(Date.now() + 4 * 60 * 60 * 1000).toISOString()
    });
  }
  
  // Generate forecast
  const forecast = [];
  for (let i = 1; i <= 5; i++) {
    const date = new Date();
    date.setDate(date.getDate() + i);
    
    forecast.push({
      date: date.toISOString().split('T')[0],
      temperature: {
        min: temperature - 3 + Math.round(Math.random() * 2),
        max: temperature + 3 + Math.round(Math.random() * 4)
      },
      condition: conditions[Math.floor(Math.random() * conditions.length)],
      windSpeed: Math.round(windSpeed + (Math.random() - 0.5) * 10),
      waveHeight: Math.round((waveHeight + (Math.random() - 0.5) * 2) * 10) / 10
    });
  }

  return {
    location: {
      name: locationName,
      lat,
      lng
    },
    current: {
      temperature,
      humidity: Math.round(60 + Math.random() * 30),
      windSpeed,
      windDirection,
      visibility: Math.round(8 + Math.random() * 7), // 8-15 nautical miles
      pressure: Math.round(1010 + (Math.random() - 0.5) * 20),
      condition,
      icon: condition.toLowerCase().replace(/\s+/g, '_')
    },
    marine: {
      waveHeight,
      swellHeight,
      swellDirection: Math.round(windDirection + (Math.random() - 0.5) * 60),
      seaTemperature: Math.round(temperature - 2 + Math.random() * 4),
      tideLevel: ['High', 'Low', 'Rising', 'Falling'][Math.floor(Math.random() * 4)]
    },
    forecast,
    alerts
  };
};

// Get weather data for a location
export const getWeatherData = async (lat: number, lng: number, locationName?: string): Promise<WeatherData> => {
  // In production, replace this with actual API call
  // Example: OpenWeatherMap API
  /*
  const API_KEY = 'your_api_key';
  const response = await fetch(
    `https://api.openweathermap.org/data/2.5/weather?lat=${lat}&lon=${lng}&appid=${API_KEY}&units=metric`
  );
  const data = await response.json();
  */
  
  // For demo, return mock data
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(generateMockWeatherData(lat, lng, locationName || `Location ${lat.toFixed(2)}, ${lng.toFixed(2)}`));
    }, 500); // Simulate API delay
  });
};

// Get weather data for multiple locations (route)
export const getRouteWeatherData = async (locations: Array<{ lat: number; lng: number; name: string }>): Promise<WeatherData[]> => {
  const weatherPromises = locations.map(location => 
    getWeatherData(location.lat, location.lng, location.name)
  );
  
  return Promise.all(weatherPromises);
};

// Get weather alerts for a route
export const getRouteAlerts = async (locations: Array<{ lat: number; lng: number; name: string }>): Promise<WeatherData['alerts']> => {
  const weatherData = await getRouteWeatherData(locations);
  
  // Combine all alerts from all locations
  const allAlerts: WeatherData['alerts'] = [];
  weatherData.forEach(data => {
    allAlerts.push(...data.alerts);
  });
  
  // Sort by severity and remove duplicates
  return allAlerts
    .sort((a, b) => {
      const severityOrder = { high: 3, medium: 2, low: 1 };
      return severityOrder[b.severity] - severityOrder[a.severity];
    })
    .filter((alert, index, arr) => 
      arr.findIndex(a => a.type === alert.type && a.message === alert.message) === index
    );
};

// Convert wind direction to compass direction
export const getWindDirection = (degrees: number): string => {
  const directions = ['N', 'NNE', 'NE', 'ENE', 'E', 'ESE', 'SE', 'SSE', 'S', 'SSW', 'SW', 'WSW', 'W', 'WNW', 'NW', 'NNW'];
  const index = Math.round(degrees / 22.5) % 16;
  return directions[index];
};

// Get weather condition icon
export const getWeatherIcon = (condition: string): string => {
  const iconMap: { [key: string]: string } = {
    'clear': '☀️',
    'partly_cloudy': '⛅',
    'cloudy': '☁️',
    'light_rain': '🌦️',
    'moderate_rain': '🌧️',
    'thunderstorms': '⛈️',
    'fog': '🌫️',
    'snow': '❄️'
  };
  
  return iconMap[condition.toLowerCase().replace(/\s+/g, '_')] || '🌤️';
};
