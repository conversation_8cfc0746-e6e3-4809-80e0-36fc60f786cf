[project]
name = "marine-dashboard"
version = "0.1.0"
description = "Marine Time Weather dashboard in Python"
authors = [
    {name = "<PERSON>"}
]
license = {text = "MIT"}
readme = "README.md"
requires-python = "^3.11"
dependencies = [
    "dash (>=3.2.0,<4.0.0)",
    "plotly (>=6.3.0,<7.0.0)",
    "folium (>=0.20.0,<0.21.0)",
    "networkx (>=3.5,<4.0)",
    "numpy (>=2.3.2,<3.0.0)",
    "pandas (>=2.3.2,<3.0.0)",
    "scipy (>=1.16.1,<2.0.0)",
    "matplotlib (>=3.10.5,<4.0.0)",
    "sqlalchemy (>=2.0.43,<3.0.0)",
    "fastapi (>=0.116.1,<0.117.0)",
    "uvicorn (>=0.35.0,<0.36.0)",
    "scikit-learn (>=1.7.1,<2.0.0)",
    "shapely (>=2.1.1,<3.0.0)",
    "geopandas (>=1.1.1,<2.0.0)",
    "pyopengl (>=3.1.10,<4.0.0)",
    "pyproj (>=3.7.2,<4.0.0)",
    "pillow (>=11.3.0,<12.0.0)",
    "requests (>=2.32.5,<3.0.0)"
]

[tool.poetry]
package-mode = false

[tool.poetry.group.dev.dependencies]
pytest = "^8.4.1"
black = "^25.1.0"
isort = "^6.0.1"
mypy = "^1.17.1"
types-requests = "^2.32.4.20250809"

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
