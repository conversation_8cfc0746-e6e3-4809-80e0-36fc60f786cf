{"name": "maritime-weather-dashboard", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.0", "@mui/material": "^5.14.0", "@tanstack/react-query": "^5.85.5", "@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@turf/boolean-point-in-polygon": "^7.2.0", "@turf/distance": "^7.2.0", "@turf/helpers": "^7.2.0", "@turf/line-intersect": "^7.2.0", "@turf/turf": "^7.2.0", "@types/jest": "^27.5.2", "@types/leaflet": "^1.9.20", "@types/node": "^16.18.126", "@types/react": "^19.1.11", "@types/react-dom": "^19.1.7", "@types/react-router-dom": "^5.3.3", "javascript-astar": "^0.4.1", "leaflet": "^1.9.4", "react": "^19.1.1", "react-dom": "^19.1.1", "react-leaflet": "^5.0.0", "react-router-dom": "^7.8.2", "react-scripts": "5.0.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "xlsx": "^0.18.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}