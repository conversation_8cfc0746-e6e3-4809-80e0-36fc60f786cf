body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  scroll-behavior: smooth;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Maritime theme animations */
@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

@keyframes glow {
  0% { box-shadow: 0 0 5px rgba(0, 255, 65, 0.5); }
  50% { box-shadow: 0 0 20px rgba(0, 255, 65, 0.8); }
  100% { box-shadow: 0 0 5px rgba(0, 255, 65, 0.5); }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.maritime-card {
  animation: fadeInUp 0.6s ease-out;
}

.maritime-card:nth-child(2) {
  animation-delay: 0.1s;
}

.maritime-card:nth-child(3) {
  animation-delay: 0.2s;
}

.maritime-card:nth-child(4) {
  animation-delay: 0.3s;
}

/* Smooth transitions for all interactive elements */
* {
  transition: all 0.3s ease;
}

/* Custom scrollbar for maritime theme */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #001122;
}

::-webkit-scrollbar-thumb {
  background: #00ff41;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #00cc33;
}
